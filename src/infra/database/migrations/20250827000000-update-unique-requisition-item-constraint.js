'use strict';

/** @type {import('sequelize-cli').Migration} */

const {
  withTimescaleDBCompression,
} = require('../utils/timescale-db-migration-helper');

module.exports = {
  async up(queryInterface, Sequelize) {
    const transaction = await queryInterface.sequelize.transaction();
    try {
      console.log('--- Starting Update Unique Constraint Migration ---');

      // Step 1: Remove the old constraint
      console.log('Step 1: Removing old unique constraint...');
      await withTimescaleDBCompression(
        queryInterface,
        transaction,
        'requisition_item_lists',
        async () => {
          await queryInterface.removeConstraint(
            'requisition_item_lists',
            'unique_requisition_item_per_requisition',
          );
        },
      );

      console.log('Step 1: Old constraint removed successfully.');

      // Step 2: Clean up any existing duplicates (keeping the first occurrence)
      console.log('Step 2: Cleaning up duplicate entries...');
      await queryInterface.sequelize.query(`
      DELETE FROM "requisition_item_lists"
      WHERE id NOT IN (
          SELECT MIN(id)
          FROM "requisition_item_lists"
          GROUP BY requisition_id, item_id
      )
      AND (requisition_id, item_id) IN (
          SELECT requisition_id, item_id
          FROM "requisition_item_lists"
          GROUP BY requisition_id, item_id
          HAVING COUNT(*) > 1
      );
    `);
      console.log('Step 2: Duplicate entries cleaned up.');

      // Step 3: Add the new constraint without created_at
      console.log('Step 3: Adding new unique constraint...');

      await withTimescaleDBCompression(
        queryInterface,
        transaction,
        'requisition_item_lists',
        async () => {
          await queryInterface.addConstraint('requisition_item_lists', {
            fields: ['requisition_id', 'item_id'],
            type: 'unique',
            name: 'unique_requisition_item_per_requisition_v2',
          });
        },
      );
      console.log('Step 3: New unique constraint added successfully.');

      console.log('--- Update Unique Constraint Migration Completed ---');
    } catch (error) {
      await transaction.rollback();
      throw error;
    }
  },

  async down(queryInterface, Sequelize) {
    console.log('--- Starting Revert Update Unique Constraint Migration ---');

    // Remove the new constraint
    console.log('Removing new unique constraint...');
    await withTimescaleDBCompression(
      queryInterface,
      transaction,
      'requisition_item_lists',
      async () => {
        await queryInterface.removeConstraint(
          'requisition_item_lists',
          'unique_requisition_item_per_requisition_v2',
        );
      },
    );
    // Add back the old constraint
    console.log('Adding back old unique constraint...');
    await withTimescaleDBCompression(
      queryInterface,
      transaction,
      'requisition_item_lists',
      async () => {
        await queryInterface.addConstraint('requisition_item_lists', {
          fields: ['requisition_id', 'item_id', 'created_at'],
          type: 'unique',
          name: 'unique_requisition_item_per_requisition',
        });
      },
    );

    console.log('--- Revert Completed ---');
  },
};
