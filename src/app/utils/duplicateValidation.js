/**
 * Utility functions for duplicate validation
 */

/**
 * Check for duplicate values in an array
 * @param {Array} array - Array to check for duplicates
 * @param {string} fieldName - Name of the field being checked (for error messages)
 * @returns {Array} Array of duplicate values
 */
function findDuplicates(array, fieldName = 'items') {
  const duplicates = array.filter((item, index) => array.indexOf(item) !== index);
  return [...new Set(duplicates)];
}

/**
 * Validate that an array contains no duplicate values
 * @param {Array} array - Array to validate
 * @param {string} fieldName - Name of the field being checked (for error messages)
 * @throws {Error} If duplicates are found
 */
function validateNoDuplicates(array, fieldName = 'items') {
  const duplicates = findDuplicates(array, fieldName);
  if (duplicates.length > 0) {
    throw new Error(`Duplicate ${fieldName} found: ${duplicates.join(', ')}`);
  }
}

/**
 * Check for duplicate item IDs in requisition item list
 * @param {Array} itemList - Array of items with itemId property
 * @returns {Array} Array of duplicate item IDs
 */
function findDuplicateItemIds(itemList) {
  const itemIds = itemList.map(item => item.itemId || item.id);
  return findDuplicates(itemIds, 'item IDs');
}

/**
 * Validate that requisition item list contains no duplicate item IDs
 * @param {Array} itemList - Array of items with itemId property
 * @throws {Error} If duplicate item IDs are found
 */
function validateNoDuplicateItemIds(itemList) {
  const duplicates = findDuplicateItemIds(itemList);
  if (duplicates.length > 0) {
    throw new Error(`Duplicate item IDs found: ${duplicates.join(', ')}`);
  }
}

module.exports = {
  findDuplicates,
  validateNoDuplicates,
  findDuplicateItemIds,
  validateNoDuplicateItemIds,
};
